#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于历史数据分析的新盈利投注系统
解决87.8%胜率仍亏损的核心问题
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from collections import Counter

from api_framework import GameAPIClient, GameMonitor, GameState
from prediction_strategy_adapter import PredictionRuleAdapter
from profitable_betting_strategy import ProfitableBettingStrategy
from real_time_logger import log_prediction, log_room_selection, log_betting, log_result
from smart_betting_handler import SmartBettingHandler

class NewProfitableSystem:
    """新盈利投注系统"""
    
    def __init__(self, api_client: GameAPIClient, config: Dict):
        """初始化新盈利系统"""
        self.api_client = api_client
        self.config = config
        self.monitor = GameMonitor(api_client)
        
        # 核心组件
        self.prediction_adapter = PredictionRuleAdapter()
        self.profitable_strategy = ProfitableBettingStrategy()
        self.smart_betting_handler = SmartBettingHandler(api_client)
        
        # 历史数据
        self.history = []
        self.load_history_data()
        
        # 系统状态
        self.is_running = False
        self.current_issue = 0
        self.session_stats = {
            'total_bets': 0,
            'total_wins': 0,
            'total_profit': 0.0,
            'max_consecutive_losses': 0,
            'current_consecutive_losses': 0
        }
        
        print("🚀 新盈利投注系统已初始化")
        print(f"📊 历史数据: {len(self.history)}期")
        print(f"💡 核心改进: 解决高胜率低盈利问题")
    
    def load_history_data(self):
        """加载历史数据"""
        try:
            with open("game_history.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.history = data.get('history', [])
            print(f"✅ 加载历史数据: {len(self.history)}期")
        except Exception as e:
            print(f"⚠️ 加载历史数据失败: {e}")
            self.history = []
    
    def get_prediction(self) -> Optional[Dict]:
        """获取预测结果"""
        
        if len(self.history) < 10:
            print("📊 历史数据不足，无法预测")
            return None
        
        recent_history = self.history[-20:] if len(self.history) >= 20 else self.history
        
        try:
            # 尝试获取预测
            prediction_result = self.prediction_adapter.predict_next_room(recent_history, 0.6)
            
            if prediction_result:
                return {
                    'room': prediction_result['predicted_room'],
                    'confidence': prediction_result['confidence'],
                    'rule_type': 'adapter'
                }
            else:
                print("📊 未找到匹配的预测规则")
                return None
                
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            return None
    
    def execute_new_betting_strategy(self, prediction: Dict):
        """执行新的投注策略"""
        
        print(f"\n🎯 新策略投注分析:")
        print(f"   预测房间: {prediction['room']}")
        print(f"   置信度: {prediction['confidence']:.3f}")
        print(f"   规则类型: {prediction['rule_type']}")
        
        # 记录预测
        log_prediction(self.current_issue, prediction['room'], 
                      prediction['confidence'], prediction['rule_type'])
        
        # 获取策略建议
        game_state = {'issue': self.current_issue, 'countdown': 25}
        strategy_result = self.profitable_strategy.execute_profitable_strategy(
            game_state, prediction, self.history
        )
        
        if strategy_result['action'] == 'skip':
            print(f"⏸️ 跳过投注: {strategy_result['reason']}")
            print(f"💡 建议: {strategy_result['recommendation']}")
            return
        
        # 执行投注
        allocation = strategy_result['allocation']
        total_amount = strategy_result['total_amount']
        
        print(f"\n💰 新策略投注:")
        print(f"   策略类型: {strategy_result['strategy_type']}")
        print(f"   风险等级: {strategy_result['risk_level']}")
        print(f"   期望收益: {strategy_result['expected_profit']:+.3f}元")
        print(f"   投注分配: {allocation}")
        print(f"   总投注额: {total_amount:.2f}元")
        
        # 记录房间选择
        available_rooms = [i for i in range(1, 9) if i != prediction['room']]
        selected_rooms = list(allocation.keys())
        
        log_room_selection(
            self.current_issue, 
            available_rooms, 
            selected_rooms[0] if len(selected_rooms) == 1 else selected_rooms,
            strategy_result['strategy_type'],
            {
                'allocation': allocation,
                'risk_level': strategy_result['risk_level'],
                'expected_profit': strategy_result['expected_profit']
            }
        )
        
        # 执行智能投注 (支持自定义金额)
        bet_results = []
        total_bet_amount = 0

        # 由于API限制，allocation只包含一个房间
        room, target_amount = list(allocation.items())[0]

        # 使用智能投注处理器
        print(f"💰 智能投注房间{room}: 目标金额{target_amount:.2f}元")

        # 获取最优金额调整建议
        optimal_amount, adjustment_note = self.smart_betting_handler.get_optimal_amount_adjustment(target_amount)
        print(f"📊 金额调整: {adjustment_note} (实际投注{optimal_amount:.2f}元)")

        # 执行智能投注
        smart_bet_result = self.smart_betting_handler.execute_smart_bet(room, target_amount)

        if smart_bet_result.success:
            print(f"✅ 房间{room}智能投注成功")
            print(f"   成功投注: {len(smart_bet_result.successful_bets)}次")
            print(f"   实际金额: {smart_bet_result.total_amount:.2f}元")

            bet_results.append({
                'room': room,
                'target_amount': target_amount,
                'actual_amount': smart_bet_result.total_amount,
                'success': True,
                'bet_count': len(smart_bet_result.successful_bets),
                'details': smart_bet_result.successful_bets
            })
            total_bet_amount = smart_bet_result.total_amount

            # 记录成功投注 (使用实际金额)
            log_betting(self.current_issue, room, smart_bet_result.total_amount, True)

        else:
            print(f"❌ 房间{room}智能投注失败: {smart_bet_result.message}")
            print(f"   失败投注: {len(smart_bet_result.failed_bets)}次")

            bet_results.append({
                'room': room,
                'target_amount': target_amount,
                'actual_amount': smart_bet_result.total_amount,
                'success': False,
                'error': smart_bet_result.message,
                'failed_details': smart_bet_result.failed_bets
            })
            total_bet_amount = smart_bet_result.total_amount

            # 记录失败投注
            log_betting(self.current_issue, room, target_amount, False)
        
        # 更新统计
        if any(result['success'] for result in bet_results):
            self.session_stats['total_bets'] += 1
            
        print(f"📊 投注完成: 总投注{total_bet_amount:.2f}元")
        
        # 存储投注信息用于后续结果分析
        self.current_bet_info = {
            'issue': self.current_issue,
            'prediction': prediction,
            'allocation': allocation,
            'total_amount': total_bet_amount,
            'strategy_result': strategy_result,
            'bet_results': bet_results
        }
    
    def analyze_betting_result(self, state: GameState):
        """分析投注结果"""
        
        if not hasattr(self, 'current_bet_info') or not self.current_bet_info:
            print("📊 本期未投注，无需分析")
            return
        
        bet_info = self.current_bet_info
        if bet_info['issue'] != state.issue:
            print("📊 期号不匹配，无需分析")
            return
        
        actual_room = state.kill_number
        allocation = bet_info['allocation']
        total_bet = bet_info['total_amount']
        
        print(f"\n📊 新策略结果分析:")
        print(f"   预测房间: {bet_info['prediction']['room']}")
        print(f"   开奖房间: {actual_room}")
        print(f"   投注分配: {allocation}")
        print(f"   总投注额: {total_bet:.2f}元")
        
        # 计算实际盈亏 (单房间投注)
        room, amount = list(allocation.items())[0]

        if room != actual_room:
            # 获胜：投注房间不是开奖房间
            profit = amount * 0.1  # 10%收益
            total_profit = profit
            result_detail = f"房间{room}获胜(+{profit:.2f}元)"
        else:
            # 失败：投注房间是开奖房间
            loss = amount
            total_profit = -loss
            result_detail = f"房间{room}失败(-{loss:.2f}元)"
        
        # 判断投注结果
        if total_profit > 0:
            result = "获胜"
            self.session_stats['total_wins'] += 1
            self.session_stats['current_consecutive_losses'] = 0
        else:
            result = "失败"
            self.session_stats['current_consecutive_losses'] += 1
            self.session_stats['max_consecutive_losses'] = max(
                self.session_stats['max_consecutive_losses'],
                self.session_stats['current_consecutive_losses']
            )

        self.session_stats['total_profit'] += total_profit

        print(f"🎯 投注结果: {result}")
        print(f"📊 详情: {result_detail}")
        print(f"💰 本期盈亏: {total_profit:+.2f}元")
        
        # 更新策略表现
        self.profitable_strategy.update_performance({
            'result': result,
            'profit': total_profit if total_profit > 0 else 0,
            'loss': abs(total_profit) if total_profit < 0 else 0
        })
        
        # 计算统计信息
        win_rate = (self.session_stats['total_wins'] / self.session_stats['total_bets'] * 100) if self.session_stats['total_bets'] > 0 else 0
        
        print(f"📈 会话统计:")
        print(f"   总投注: {self.session_stats['total_bets']}次")
        print(f"   获胜: {self.session_stats['total_wins']}次")
        print(f"   胜率: {win_rate:.1f}%")
        print(f"   总盈亏: {self.session_stats['total_profit']:+.2f}元")
        print(f"   连败: {self.session_stats['current_consecutive_losses']}次")
        print(f"   最大连败: {self.session_stats['max_consecutive_losses']}次")
        
        # 记录结果
        log_result(state.issue, actual_room, result, total_profit)
        
        # 清除当前投注信息
        self.current_bet_info = None
    
    def process_game_state(self, state: GameState):
        """处理游戏状态"""
        
        if state.state == 1:  # 等待开奖 - 投注时机
            self.handle_betting_opportunity(state)
        elif state.state == 2:  # 已开奖 - 分析结果
            self.handle_lottery_result(state)
    
    def handle_betting_opportunity(self, state: GameState):
        """处理投注时机"""
        
        print(f"\n🎯 投注时机: 期号{state.issue}, 倒计时{state.countdown}秒")
        
        self.current_issue = state.issue
        
        # 投注时机判断
        if not (15 <= state.countdown <= 30):
            print(f"⏰ 投注时机不佳，倒计时{state.countdown}秒")
            return
        
        # 获取预测
        prediction = self.get_prediction()
        if not prediction:
            print("📊 无可用预测，跳过投注")
            return
        
        # 执行新投注策略
        self.execute_new_betting_strategy(prediction)
    
    def handle_lottery_result(self, state: GameState):
        """处理开奖结果"""
        
        print(f"\n🎯 开奖结果: 期号{state.issue}, 开出房间{state.kill_number}")
        
        # 分析投注结果
        self.analyze_betting_result(state)
        
        # 更新历史数据
        if state.kill_number > 0:
            self.history.append(state.kill_number)
            self.save_history_data()
            print(f"📊 历史数据更新: 共{len(self.history)}期")
    
    def save_history_data(self):
        """保存历史数据"""
        try:
            data = {
                'history': self.history,
                'last_updated': datetime.now().isoformat(),
                'total_periods': len(self.history)
            }
            
            with open("game_history.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ 保存历史数据失败: {e}")
    
    def start_monitoring(self):
        """启动新盈利系统"""
        print("\n🚀 启动新盈利投注系统...")
        print("💡 核心改进:")
        print("   1. 动态投注金额调整")
        print("   2. 智能单房间选择")
        print("   3. 高置信度风险控制")
        print("   4. 时间段风险管理")
        print("   5. 连败保护机制")
        
        self.is_running = True
        self.monitor.start_monitoring(self.process_game_state)

def main():
    """主函数"""
    
    print("🎯 新盈利投注系统")
    print("=" * 50)
    print("基于历史数据分析，解决高胜率低盈利问题")
    print()
    
    # API配置
    api_config = {
        'base_url': 'https://fks-api.lucklyworld.com',
        'headers': {
            'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
            'packageId': 'com.caike.union',
            'version': '5.2.2',
            'channel': 'official',
            'androidId': 'e21953ffb86fa7a8',
            'userId': '8607652',
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA3NjUyIiwiaWF0IjoxNzUzNDM1MzIxLCJuYmYiOjE3NTM0MzUzMjEsImV4cCI6MTc1NjAyNzMyMSwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.2BTy855tgkF57kscdo1NTHbsygZUBn93W1vM5vmrXAo',
            'IMEI': '',
            'ts': str(int(time.time() * 1000)),
            'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'fks-api.lucklyworld.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
    }
    
    # 系统配置
    config = {
        'base_bet_amount': 1.0,
        'max_bet_amount': 10.0,
        'profit_target': 50.0,
        'stop_loss': -20.0
    }
    
    try:
        # 创建API客户端
        api_client = GameAPIClient(api_config['base_url'], api_config['headers'])
        
        # 创建新盈利系统
        system = NewProfitableSystem(api_client, config)
        
        response = input("是否启动新盈利投注系统？(yes/no): ").lower().strip()
        
        if response in ['yes', 'y', '是']:
            system.start_monitoring()
        else:
            print("系统未启动")
            
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
