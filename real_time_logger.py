#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据记录器
将系统运行数据实时写入文档
"""

import json
import csv
import time
from datetime import datetime
from typing import Dict, List, Any
import os

class RealTimeLogger:
    """实时数据记录器"""
    
    def __init__(self, base_filename: str = "betting_log"):
        """初始化记录器"""
        self.base_filename = base_filename
        self.session_start = datetime.now()
        
        # 生成带时间戳的文件名
        timestamp = self.session_start.strftime('%Y%m%d_%H%M%S')
        self.json_file = f"{base_filename}_{timestamp}.json"
        self.csv_file = f"{base_filename}_{timestamp}.csv"
        self.md_file = f"{base_filename}_{timestamp}.md"
        
        # 初始化数据结构
        self.session_data = {
            'session_info': {
                'start_time': self.session_start.isoformat(),
                'session_id': timestamp,
                'total_bets': 0,
                'total_wins': 0,
                'total_profit': 0.0
            },
            'betting_records': []
        }
        
        # 初始化文件
        self.init_files()
        
        print(f"📝 实时记录器已启动")
        print(f"   JSON文件: {self.json_file}")
        print(f"   CSV文件: {self.csv_file}")
        print(f"   Markdown文件: {self.md_file}")
    
    def init_files(self):
        """初始化记录文件"""
        
        # 初始化JSON文件
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        
        # 初始化CSV文件
        csv_headers = [
            '时间', '期号', '预测房间', '预测置信度', '规则类型',
            '可选房间', '投注房间', '选择策略', '投注金额',
            '开奖房间', '投注结果', '盈亏', '累计盈亏', '胜率'
        ]
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(csv_headers)
        
        # 初始化Markdown文件
        with open(self.md_file, 'w', encoding='utf-8') as f:
            f.write(f"# 🎯 投注系统实时记录\n\n")
            f.write(f"**会话开始时间**: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## 📊 实时统计\n\n")
            f.write(f"- **总投注次数**: 0\n")
            f.write(f"- **获胜次数**: 0\n")
            f.write(f"- **胜率**: 0.0%\n")
            f.write(f"- **总盈亏**: +0.00元\n\n")
            f.write(f"## 📋 投注记录\n\n")
            f.write(f"| 时间 | 期号 | 预测房间 | 置信度 | 投注房间 | 选择策略 | 开奖房间 | 结果 | 盈亏 |\n")
            f.write(f"|------|------|----------|--------|----------|----------|----------|------|------|\n")
    
    def log_prediction(self, issue: int, predicted_room: int, confidence: float, rule_type: str):
        """记录预测信息"""
        
        current_time = datetime.now()
        
        # 创建预测记录
        prediction_record = {
            'timestamp': current_time.isoformat(),
            'time_str': current_time.strftime('%H:%M:%S'),
            'issue': issue,
            'predicted_room': predicted_room,
            'confidence': confidence,
            'rule_type': rule_type,
            'status': 'predicted'
        }
        
        # 添加到会话数据
        self.session_data['betting_records'].append(prediction_record)
        
        print(f"📝 记录预测: 期号{issue}, 预测房间{predicted_room}, 置信度{confidence:.3f}")
    
    def log_room_selection(self, issue: int, available_rooms: List[int], selected_room: int, 
                          strategy: str, selection_details: Dict):
        """记录智能房间选择过程"""
        
        # 找到对应的预测记录
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and record['status'] == 'predicted':
                # 更新记录
                record.update({
                    'available_rooms': available_rooms,
                    'selected_room': selected_room,
                    'selection_strategy': strategy,
                    'selection_details': selection_details,
                    'status': 'room_selected'
                })
                break
        
        print(f"📝 记录房间选择: 期号{issue}, 选择房间{selected_room}, 策略{strategy}")
    
    def log_betting(self, issue: int, bet_room: int, bet_amount: float, bet_success: bool):
        """记录投注信息"""
        
        # 找到对应的记录
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and 'selected_room' in record:
                # 更新记录
                record.update({
                    'bet_room': bet_room,
                    'bet_amount': bet_amount,
                    'bet_success': bet_success,
                    'status': 'bet_placed' if bet_success else 'bet_failed'
                })
                
                if bet_success:
                    self.session_data['session_info']['total_bets'] += 1
                
                break
        
        print(f"📝 记录投注: 期号{issue}, 投注房间{bet_room}, 金额{bet_amount}, 成功{bet_success}")
    
    def log_result(self, issue: int, actual_room: int, result: str, profit: float):
        """记录开奖结果"""
        
        # 找到对应的记录
        for record in reversed(self.session_data['betting_records']):
            if record['issue'] == issue and record.get('status') == 'bet_placed':
                # 更新记录
                record.update({
                    'actual_room': actual_room,
                    'result': result,
                    'profit': profit,
                    'status': 'completed'
                })
                
                # 更新会话统计
                if result == '获胜':
                    self.session_data['session_info']['total_wins'] += 1
                
                self.session_data['session_info']['total_profit'] += profit
                
                # 立即写入所有文件
                self.write_all_files()
                
                break
        
        print(f"📝 记录结果: 期号{issue}, 开奖房间{actual_room}, 结果{result}, 盈亏{profit:+.2f}")
    
    def write_all_files(self):
        """写入所有记录文件"""
        
        # 更新JSON文件
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        
        # 更新CSV文件
        self.write_csv_file()
        
        # 更新Markdown文件
        self.write_markdown_file()
        
        print(f"📝 已更新所有记录文件")
    
    def write_csv_file(self):
        """写入CSV文件"""
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = [
                '时间', '期号', '预测房间', '预测置信度', '规则类型',
                '可选房间', '投注房间', '选择策略', '投注金额',
                '开奖房间', '投注结果', '盈亏', '累计盈亏', '胜率'
            ]
            writer.writerow(headers)
            
            # 写入数据
            cumulative_profit = 0
            completed_bets = 0
            wins = 0
            
            for record in self.session_data['betting_records']:
                if record.get('status') == 'completed':
                    completed_bets += 1
                    if record.get('result') == '获胜':
                        wins += 1
                    
                    cumulative_profit += record.get('profit', 0)
                    win_rate = (wins / completed_bets) * 100 if completed_bets > 0 else 0
                    
                    row = [
                        record.get('time_str', ''),
                        record.get('issue', ''),
                        record.get('predicted_room', ''),
                        f"{record.get('confidence', 0):.3f}",
                        record.get('rule_type', ''),
                        str(record.get('available_rooms', [])),
                        record.get('bet_room', ''),
                        record.get('selection_strategy', ''),
                        f"{record.get('bet_amount', 0):.2f}",
                        record.get('actual_room', ''),
                        record.get('result', ''),
                        f"{record.get('profit', 0):+.2f}",
                        f"{cumulative_profit:+.2f}",
                        f"{win_rate:.1f}%"
                    ]
                    writer.writerow(row)
    
    def write_markdown_file(self):
        """写入Markdown文件"""
        
        session_info = self.session_data['session_info']
        total_bets = session_info['total_bets']
        total_wins = session_info['total_wins']
        total_profit = session_info['total_profit']
        win_rate = (total_wins / total_bets) * 100 if total_bets > 0 else 0
        
        with open(self.md_file, 'w', encoding='utf-8') as f:
            f.write(f"# 🎯 投注系统实时记录\n\n")
            f.write(f"**会话开始时间**: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**最后更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## 📊 实时统计\n\n")
            f.write(f"- **总投注次数**: {total_bets}\n")
            f.write(f"- **获胜次数**: {total_wins}\n")
            f.write(f"- **胜率**: {win_rate:.1f}%\n")
            f.write(f"- **总盈亏**: {total_profit:+.2f}元\n\n")
            
            f.write(f"## 📋 详细投注记录\n\n")
            f.write(f"| 时间 | 期号 | 预测房间 | 置信度 | 投注房间 | 选择策略 | 开奖房间 | 结果 | 盈亏 |\n")
            f.write(f"|------|------|----------|--------|----------|----------|----------|------|------|\n")
            
            # 写入完成的记录
            for record in self.session_data['betting_records']:
                if record.get('status') == 'completed':
                    f.write(f"| {record.get('time_str', '')} | ")
                    f.write(f"{record.get('issue', '')} | ")
                    f.write(f"{record.get('predicted_room', '')} | ")
                    f.write(f"{record.get('confidence', 0):.3f} | ")
                    f.write(f"{record.get('bet_room', '')} | ")
                    f.write(f"{record.get('selection_strategy', '')} | ")
                    f.write(f"{record.get('actual_room', '')} | ")
                    f.write(f"{record.get('result', '')} | ")
                    f.write(f"{record.get('profit', 0):+.2f}元 |\n")
            
            f.write(f"\n## 🎯 智能房间选择详情\n\n")
            
            # 写入房间选择详情
            for i, record in enumerate(self.session_data['betting_records']):
                if record.get('status') == 'completed' and 'selection_details' in record:
                    f.write(f"### 期号{record.get('issue', '')} - 第{i+1}次投注\n\n")
                    f.write(f"- **预测房间**: {record.get('predicted_room', '')} (置信度: {record.get('confidence', 0):.3f})\n")
                    f.write(f"- **可选房间**: {record.get('available_rooms', [])}\n")
                    f.write(f"- **选择策略**: {record.get('selection_strategy', '')}\n")
                    f.write(f"- **投注房间**: {record.get('bet_room', '')}\n")
                    f.write(f"- **开奖房间**: {record.get('actual_room', '')}\n")
                    f.write(f"- **投注结果**: {record.get('result', '')}\n")
                    f.write(f"- **盈亏**: {record.get('profit', 0):+.2f}元\n\n")
                    
                    # 添加选择详情
                    details = record.get('selection_details', {})
                    if details:
                        f.write(f"**选择详情**:\n")
                        for key, value in details.items():
                            f.write(f"- {key}: {value}\n")
                        f.write(f"\n")
    
    def get_summary(self) -> Dict:
        """获取会话摘要"""
        
        session_info = self.session_data['session_info']
        total_bets = session_info['total_bets']
        total_wins = session_info['total_wins']
        total_profit = session_info['total_profit']
        win_rate = (total_wins / total_bets) * 100 if total_bets > 0 else 0
        
        return {
            'session_duration': str(datetime.now() - self.session_start),
            'total_bets': total_bets,
            'total_wins': total_wins,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'files': {
                'json': self.json_file,
                'csv': self.csv_file,
                'markdown': self.md_file
            }
        }

# 全局记录器实例
_logger_instance = None

def get_logger(base_filename: str = "betting_log") -> RealTimeLogger:
    """获取全局记录器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = RealTimeLogger(base_filename)
    return _logger_instance

def log_prediction(issue: int, predicted_room: int, confidence: float, rule_type: str):
    """记录预测信息"""
    logger = get_logger()
    logger.log_prediction(issue, predicted_room, confidence, rule_type)

def log_room_selection(issue: int, available_rooms: List[int], selected_room: int, 
                      strategy: str, selection_details: Dict):
    """记录房间选择"""
    logger = get_logger()
    logger.log_room_selection(issue, available_rooms, selected_room, strategy, selection_details)

def log_betting(issue: int, bet_room: int, bet_amount: float, bet_success: bool):
    """记录投注"""
    logger = get_logger()
    logger.log_betting(issue, bet_room, bet_amount, bet_success)

def log_result(issue: int, actual_room: int, result: str, profit: float):
    """记录结果"""
    logger = get_logger()
    logger.log_result(issue, actual_room, result, profit)

def get_summary() -> Dict:
    """获取摘要"""
    logger = get_logger()
    return logger.get_summary()

if __name__ == "__main__":
    # 测试记录器
    logger = RealTimeLogger("test_log")
    
    # 模拟一次完整的投注过程
    logger.log_prediction(123456, 8, 0.750, "dynamic")
    logger.log_room_selection(123456, [1,2,3,4,5,6,7], 4, "映射策略", {"mapping": "8->4"})
    logger.log_betting(123456, 4, 10.0, True)
    logger.log_result(123456, 2, "获胜", 1.0)
    
    print("📊 测试完成，请查看生成的文件")
